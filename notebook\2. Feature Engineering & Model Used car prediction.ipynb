{"cells": [{"cell_type": "markdown", "id": "489b6ded", "metadata": {}, "source": ["![image info](https://ineuron.ai/images/ineuron-logo.png)"]}, {"cell_type": "markdown", "id": "4437a980", "metadata": {}, "source": ["# Data Pre-Processing"]}, {"cell_type": "markdown", "id": "74b4d7e0", "metadata": {}, "source": ["#### Import Packages and CSV"]}, {"cell_type": "code", "execution_count": 1, "id": "5530cc59", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(15411, 14)\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "pd.pandas.set_option(\"display.max_columns\", None)\n", "# Create Dataframe\n", "df = pd.read_csv(\"./data/cardekho_dataset.csv\")\n", "\n", "# Print shape of dataset\n", "print(df.shape)"]}, {"cell_type": "markdown", "id": "c27265bd", "metadata": {}, "source": ["## Data Cleaning"]}, {"cell_type": "markdown", "id": "dbad805a", "metadata": {}, "source": ["### Handling Missing values"]}, {"cell_type": "markdown", "id": "0a0c1c0d", "metadata": {}, "source": ["* Handling Missing values \n", "* Handling Duplicates\n", "* Check data type\n", "* Understand the dataset"]}, {"cell_type": "markdown", "id": "40b4a428", "metadata": {}, "source": ["#### Check Null Values"]}, {"cell_type": "code", "execution_count": 2, "id": "2b94aa8f", "metadata": {}, "outputs": [{"data": {"text/plain": ["Unnamed: 0           0\n", "car_name             0\n", "brand                0\n", "model                0\n", "vehicle_age          0\n", "km_driven            0\n", "seller_type          0\n", "fuel_type            0\n", "transmission_type    0\n", "mileage              0\n", "engine               0\n", "max_power            0\n", "seats                0\n", "selling_price        0\n", "dtype: int64"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["##Check features with nan value\n", "df.isnull().sum()"]}, {"cell_type": "markdown", "id": "791085e9", "metadata": {}, "source": ["**Report** \n", "- There are No null values in the data "]}, {"cell_type": "markdown", "id": "76eafe12", "metadata": {}, "source": ["### 3.2 Other Data Cleaning steps"]}, {"cell_type": "markdown", "id": "471fd48f", "metadata": {}, "source": ["**Handling Duplicates**"]}, {"cell_type": "code", "execution_count": 3, "id": "d8fa17e0", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df.duplicated().sum()"]}, {"cell_type": "markdown", "id": "6f868b71", "metadata": {}, "source": ["**Report**\n", "* No Duplicates in the dataset."]}, {"cell_type": "markdown", "id": "6d48a184", "metadata": {}, "source": ["# Feature Engineering"]}, {"cell_type": "code", "execution_count": 4, "id": "e3fceb09", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>car_name</th>\n", "      <th>brand</th>\n", "      <th>model</th>\n", "      <th>vehicle_age</th>\n", "      <th>km_driven</th>\n", "      <th>seller_type</th>\n", "      <th>fuel_type</th>\n", "      <th>transmission_type</th>\n", "      <th>mileage</th>\n", "      <th>engine</th>\n", "      <th>max_power</th>\n", "      <th>seats</th>\n", "      <th>selling_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3799</th>\n", "      <td>4845</td>\n", "      <td>Ferrari GTC4Lusso</td>\n", "      <td>Ferrari</td>\n", "      <td>GTC4Lusso</td>\n", "      <td>2</td>\n", "      <td>3800</td>\n", "      <td>Dealer</td>\n", "      <td>Petrol</td>\n", "      <td>Automatic</td>\n", "      <td>4.0</td>\n", "      <td>3855</td>\n", "      <td>601.0</td>\n", "      <td>4</td>\n", "      <td>39500000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Unnamed: 0           car_name    brand      model  vehicle_age  \\\n", "3799        4845  Ferrari GTC4Lusso  Ferrari  GTC4Lusso            2   \n", "\n", "      km_driven seller_type fuel_type transmission_type  mileage  engine  \\\n", "3799       3800      Dealer    Petrol         Automatic      4.0    3855   \n", "\n", "      max_power  seats  selling_price  \n", "3799      601.0      4       39500000  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df.loc[df['brand']== 'Ferrari']"]}, {"cell_type": "markdown", "id": "a935debc", "metadata": {}, "source": ["#### Brand and model column can be dropped as the information is already available in car_name"]}, {"cell_type": "code", "execution_count": 5, "id": "d785a2f9", "metadata": {}, "outputs": [], "source": ["df.drop(['brand','model', 'Unnamed: 0'], axis=1, inplace=True)"]}, {"cell_type": "markdown", "id": "66542de1", "metadata": {}, "source": ["### Type of Features"]}, {"cell_type": "markdown", "id": "85df52e5", "metadata": {}, "source": ["**Numeric Features**"]}, {"cell_type": "code", "execution_count": 6, "id": "baff199f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Num of Numerical Features : 7\n"]}], "source": ["num_features = [feature for feature in df.columns if df[feature].dtype != 'O']\n", "print('Num of Numerical Features :', len(num_features))"]}, {"cell_type": "markdown", "id": "e1107060", "metadata": {}, "source": ["**Categorical Features**"]}, {"cell_type": "code", "execution_count": 7, "id": "ff997805", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Num of Categorical Features : 4\n"]}], "source": ["cat_features = [feature for feature in df.columns if df[feature].dtype == 'O']\n", "print('Num of Categorical Features :', len(cat_features))"]}, {"cell_type": "markdown", "id": "3ca43676", "metadata": {}, "source": ["**Discrete Features**"]}, {"cell_type": "code", "execution_count": 8, "id": "812ee6e0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Num of Discrete Features : 2\n"]}], "source": ["discrete_features=[feature for feature in num_features if len(df[feature].unique())<=25]\n", "print('Num of Discrete Features :',len(discrete_features))"]}, {"cell_type": "markdown", "id": "3e6740bf", "metadata": {}, "source": ["**Continues Features**"]}, {"cell_type": "code", "execution_count": 9, "id": "e501c72b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Num of Continuous Features : 5\n"]}], "source": ["continuous_features=[feature for feature in num_features if feature not in discrete_features]\n", "print('Num of Continuous Features :',len(continuous_features))"]}, {"cell_type": "markdown", "id": "7a9c40c8", "metadata": {}, "source": ["### Multicollinearity Check\n", "#### Correllation Heatmap"]}, {"cell_type": "code", "execution_count": 10, "id": "f6ebeaea", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1080x720 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize = (15,10))\n", "sns.heatmap(df.corr(), cmap=\"CMRmap_r\", annot=True, mask= np.triu(df.corr(), k=1))\n", "plt.show()"]}, {"cell_type": "markdown", "id": "df09f4a4", "metadata": {}, "source": ["**Report** \n", "* Max power and Engine cc are highly correlated.\n", "* We can check Vif to remove any highly correlated independent features."]}, {"cell_type": "markdown", "id": "a5ad69d6", "metadata": {}, "source": ["#### Variance Inflation Factor (VIF)"]}, {"cell_type": "markdown", "id": "bcc04f4d", "metadata": {}, "source": ["* Multicollinearity occurs when there are two or more independent variables in a multiple regression model, which have a high correlation among themselves. When some features are highly correlated.\n", "* Multicollinearity can be detected using various techniques, one such technique being the Variance Inflation Factor(VIF)."]}, {"cell_type": "code", "execution_count": 11, "id": "f3c49115", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Variance Inflation Factor of vehicle_age column is 1.34 \n", "\n", "Variance Inflation Factor of km_driven column is 1.24 \n", "\n", "Variance Inflation Factor of mileage column is 1.54 \n", "\n", "Variance Inflation Factor of engine column is 2.41 \n", "\n", "Variance Inflation Factor of max_power column is 3.13 \n", "\n", "Variance Inflation Factor of seats column is 1.77 \n", "\n"]}], "source": ["import statsmodels.api as sm\n", "num_features = df.select_dtypes(exclude=\"object\").columns\n", "for i in range(0, len(num_features)-1):\n", "    x = df.loc[:, df.columns == num_features[i]]\n", "    y = df.selling_price\n", "    model = sm.OLS(x,y)\n", "    results = model.fit()\n", "    rsq = results.rsquared\n", "    vif = round(1 / (1 - rsq), 2)\n", "    print(\n", "        \"Variance Inflation Factor of {} column is {} \\n\".format(\n", "            num_features[i], vif)\n", "        )"]}, {"cell_type": "markdown", "id": "d5a93f6a", "metadata": {}, "source": [" ” VIF determines the strength of the correlation between the independent variables. It is predicted by taking a variable and regressing it against every other variable “ \n", " \n", "**- This Dataset have MultiCollinearity in max_power column but not so much** \n", "\n", "- Max Power and Engine are correlated but we can consider these two features because they hold important information for a vechicle."]}, {"cell_type": "markdown", "id": "d234a954", "metadata": {}, "source": ["### Check Outlier and Capping it"]}, {"cell_type": "markdown", "id": "29b1ab3b", "metadata": {}, "source": ["**Why outliers?**\n", "* Data Entry error : Human error. \n", "* Measurement error: Instrument error. \n", "* Natural error: it will be Real life data.\n", "* Intentional error: People give wrong inputs\n", "\n", "**Impact of Outliers ?**\n", "* <PERSON>uliers can very high impact on few Machine learning models.\n", "* Can Cause bias in the output."]}, {"cell_type": "markdown", "id": "b2c78731", "metadata": {}, "source": ["**Why IQR For Outlier Handling?**\n", "* For Skewed distributions: Use Inter-Quartile Range (IQR) proximity rule.\n", "\n", "* As some columns are skewed we go with IQR method for outlier treatment\n", "\n", "* The data points which fall below Q1 – 1.5 IQR or above Q3 + 1.5 IQR are outliers.\n", "\n", "* where Q1 and Q3 are the 25th and 75th percentile of the dataset respectively, and IQR represents the inter-quartile range and given by Q3 – Q1."]}, {"cell_type": "code", "execution_count": 12, "id": "8e580ec1", "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for i in continuous_features:\n", "        plt.figure(figsize=(10,6))\n", "        sns.set_style('ticks')\n", "        ax = sns.boxplot(df[i])"]}, {"cell_type": "markdown", "id": "6770c24d", "metadata": {}, "source": ["**Detecting Outlier and Capping it**"]}, {"cell_type": "code", "execution_count": 13, "id": "6ec840e1", "metadata": {}, "outputs": [], "source": ["def detect_outliers(col):\n", "    # Finding the IQR\n", "    percentile25 = df[col].quantile(0.25)\n", "    percentile75 = df[col].quantile(0.75)\n", "    print('\\n ####', col , '####')\n", "    print(\"percentile25\",percentile25)\n", "    print(\"percentile75\",percentile75)\n", "    iqr = percentile75 - percentile25\n", "    upper_limit = percentile75 + 1.5 * iqr\n", "    lower_limit = percentile25 - 1.5 * iqr\n", "    print(\"Upper limit\",upper_limit)\n", "    print(\"Lower limit\",lower_limit)\n", "    df.loc[(df[col]>upper_limit), col]= upper_limit\n", "    df.loc[(df[col]<lower_limit), col]= lower_limit    \n", "    return df"]}, {"cell_type": "markdown", "id": "b1ec524f", "metadata": {}, "source": ["**Why are we capping it and why not trim it ?**\n", "* Trimming outliers may result in the removal of a large number of records from this dataset as we have already very less rows so this isn’t desirable in this case since columns other than the ones containing the outlier values may contain useful information.\n", "\n", "* In this cases, you can use outlier capping to replace the outlier values with a maximum or minimum capped values. Be warned, this manipulates our data but we can replace outlier values by the upper and lower limit calculated using the IQR range."]}, {"cell_type": "code", "execution_count": 14, "id": "1236741e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " #### km_driven ####\n", "percentile25 30000.0\n", "percentile75 70000.0\n", "Upper limit 130000.0\n", "Lower limit -30000.0\n", "\n", " #### mileage ####\n", "percentile25 17.0\n", "percentile75 22.7\n", "Upper limit 31.25\n", "Lower limit 8.450000000000001\n", "\n", " #### engine ####\n", "percentile25 1197.0\n", "percentile75 1582.0\n", "Upper limit 2159.5\n", "Lower limit 619.5\n", "\n", " #### max_power ####\n", "percentile25 74.0\n", "percentile75 117.3\n", "Upper limit 182.25\n", "Lower limit 9.050000000000011\n", "\n", " #### selling_price ####\n", "percentile25 385000.0\n", "percentile75 825000.0\n", "Upper limit 1485000.0\n", "Lower limit -275000.0\n"]}], "source": ["for col in continuous_features:\n", "         detect_outliers(col)"]}, {"cell_type": "markdown", "id": "a890a255", "metadata": {}, "source": ["**Checking Skewness after Outlier Capping**"]}, {"cell_type": "code", "execution_count": 15, "id": "4038a207", "metadata": {}, "outputs": [{"data": {"text/plain": ["km_driven        0.617437\n", "mileage          0.067940\n", "engine           0.684096\n", "max_power        1.067229\n", "selling_price    0.968836\n", "dtype: float64"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["df[continuous_features].skew(axis=0, skipna=True)"]}, {"cell_type": "markdown", "id": "ca2d817d", "metadata": {}, "source": ["## Feature Transformation"]}, {"cell_type": "code", "execution_count": 16, "id": "a921c3a0", "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x576 with 5 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# distribution of data before scaling\n", "plt.figure(figsize=(12, 8))\n", "for i, col in enumerate(['km_driven', 'mileage', 'engine', 'max_power', 'selling_price']):\n", "    plt.subplot(3, 2, i+1)\n", "    sns.kdeplot(x=df[col], color='indianred')\n", "    plt.xlabel(col)\n", "    plt.tight_layout()"]}, {"cell_type": "markdown", "id": "1786c0ad", "metadata": {}, "source": ["**Report**\n", "* After Capping Outlier it is all column's distribution is Normal so transformation is not required."]}, {"cell_type": "markdown", "id": "5a2cf140", "metadata": {}, "source": ["### Split X and Y"]}, {"cell_type": "code", "execution_count": 17, "id": "606bd5ec", "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split\n", "X = df.drop(['selling_price'], axis=1)\n", "y = df['selling_price']"]}, {"cell_type": "markdown", "id": "9455c01a", "metadata": {}, "source": ["* **Split Dataframe to X and y**\n", "* **Here we set a variable X i.e, independent columns, and a variable y i.e, dependent column as the “selling price” column.**\n"]}, {"cell_type": "markdown", "id": "3c1d7480", "metadata": {}, "source": ["### Feature Selection"]}, {"cell_type": "code", "execution_count": 18, "id": "00e06fc4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["car_name : 121\n", "seller_type : 3\n", "fuel_type : 5\n", "transmission_type : 2\n"]}], "source": ["for feature in cat_features:\n", "    print(feature,':', X[feature].nunique())"]}, {"cell_type": "markdown", "id": "51a3853d", "metadata": {}, "source": ["## Feature Encoding and Scaling"]}, {"cell_type": "markdown", "id": "29dbaf2d", "metadata": {}, "source": ["**One Hot Encoding for Columns which had lesser unique values and not ordinal**\n", "* One hot encoding is a process by which categorical variables are converted into a form that could be provided to ML algorithms to do a better job in prediction.\n", "\n", "**Binary Encoder**\n", "* To fight the curse of dimensionality, binary encoding might be a good alternative to one-hot encoding because it creates fewer columns when encoding categorical variables."]}, {"cell_type": "code", "execution_count": 19, "id": "348ccd3f", "metadata": {}, "outputs": [], "source": ["# Create Column Transformer with 3 types of transformers\n", "num_features = X.select_dtypes(exclude=\"object\").columns\n", "onehot_columns = ['seller_type','fuel_type','transmission_type']\n", "binary_columns = ['car_name']\n", "\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "from category_encoders.binary import BinaryEncoder\n", "from sklearn.compose import ColumnTransformer\n", "\n", "numeric_transformer = StandardScaler()\n", "oh_transformer = OneHotEncoder()\n", "binary_transformer = BinaryEncoder()\n", "\n", "preprocessor = ColumnTransformer(\n", "    [\n", "        (\"OneHotEncoder\", oh_transformer, onehot_columns),\n", "         (\"StandardScaler\", numeric_transformer, num_features),\n", "        (\"BinaryEncoder\", binary_transformer, binary_columns)\n", "        \n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": 20, "id": "073fea39", "metadata": {}, "outputs": [], "source": ["X= preprocessor.fit_transform(X)"]}, {"cell_type": "markdown", "id": "91bcc975", "metadata": {}, "source": ["## Train Test Split"]}, {"cell_type": "markdown", "id": "dbdad979", "metadata": {}, "source": ["* The train-test split procedure is used to estimate the performance of machine learning algorithms when they are used to make predictions on data not used to train the model.\n", "\n", "* It is a fast and easy procedure to perform, the results of which allow you to compare the performance of machine learning algorithms."]}, {"cell_type": "code", "execution_count": 21, "id": "1531b2c6", "metadata": {}, "outputs": [{"data": {"text/plain": ["((12328, 23), (3083, 23))"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# separate dataset into train and test\n", "X_train, X_test, y_train, y_test = train_test_split(X,y,test_size=0.2,random_state=42)\n", "X_train.shape, X_test.shape"]}, {"cell_type": "markdown", "id": "d81b9eaa", "metadata": {}, "source": ["## Model Selection\n", "* **Here should understand the Various Regression models with default values from these models we can choose top 4 with Highest Accuracy score and proceed with HyperParameter Tuning**"]}, {"cell_type": "markdown", "id": "8b9e9fab", "metadata": {}, "source": ["**Import Required packages for model training**"]}, {"cell_type": "code", "execution_count": 22, "id": "d82663ae", "metadata": {}, "outputs": [], "source": ["from sklearn.metrics import mean_squared_error, r2_score\n", "from sklearn.neighbors import KNeighborsRegressor\n", "from sklearn.tree import DecisionTreeRegressor\n", "from sklearn.ensemble import RandomForestRegressor,AdaBoostRegressor\n", "from sklearn.svm import SVR\n", "from sklearn.linear_model import LinearRegression, Ridge,Lasso\n", "from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error\n", "from sklearn.model_selection import RandomizedSearchCV\n", "from catboost import CatBoostRegressor\n", "from xgboost import XGBRegressor"]}, {"cell_type": "markdown", "id": "7aca3c34", "metadata": {}, "source": ["#### Create a Function to Evaluate Model"]}, {"cell_type": "code", "execution_count": 23, "id": "cae770cd", "metadata": {}, "outputs": [], "source": ["def evaluate_model(true, predicted):\n", "    mae = mean_absolute_error(true, predicted)\n", "    mse = mean_squared_error(true, predicted)\n", "    rmse = np.sqrt(mean_squared_error(true, predicted))\n", "    r2_square = r2_score(true, predicted)\n", "    return mae, rmse, r2_square"]}, {"cell_type": "code", "execution_count": 24, "id": "6407432b", "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Linear Regression\n", "Model performance for Training set\n", "- Root Mean Squared Error: 148796.0465\n", "- Mean Absolute Error: 112876.7657\n", "- R2 Score: 0.8379\n", "----------------------------------\n", "Model performance for Test set\n", "- Root Mean Squared Error: 155478.5970\n", "- Mean Absolute Error: 118466.1132\n", "- R2 Score: 0.8343\n", "===================================\n", "\n", "\n", "<PERSON><PERSON>\n", "Model performance for Training set\n", "- Root Mean Squared Error: 148767.4172\n", "- Mean Absolute Error: 112822.8031\n", "- R2 Score: 0.8380\n", "----------------------------------\n", "Model performance for Test set\n", "- Root Mean Squared Error: 155479.5196\n", "- Mean Absolute Error: 118418.6806\n", "- R2 Score: 0.8343\n", "===================================\n", "\n", "\n", "Ridge\n", "Model performance for Training set\n", "- Root Mean Squared Error: 148770.8205\n", "- Mean Absolute Error: 112835.0850\n", "- R2 Score: 0.8380\n", "----------------------------------\n", "Model performance for Test set\n", "- Root Mean Squared Error: 155485.9482\n", "- Mean Absolute Error: 118436.6096\n", "- R2 Score: 0.8343\n", "===================================\n", "\n", "\n", "K-N<PERSON><PERSON><PERSON><PERSON>\n", "Model performance for Training set\n", "- Root Mean Squared Error: 82056.9057\n", "- Mean Absolute Error: 54835.7722\n", "- R2 Score: 0.9507\n", "----------------------------------\n", "Model performance for Test set\n", "- Root Mean Squared Error: 103068.9977\n", "- Mean Absolute Error: 68802.5057\n", "- R2 Score: 0.9272\n", "===================================\n", "\n", "\n", "Decision Tree\n", "Model performance for Training set\n", "- Root Mean Squared Error: 19892.7304\n", "- Mean Absolute Error: 5386.6533\n", "- R2 Score: 0.9971\n", "----------------------------------\n", "Model performance for Test set\n", "- Root Mean Squared Error: 124194.5089\n", "- Mean Absolute Error: 79873.0592\n", "- R2 Score: 0.8943\n", "===================================\n", "\n", "\n", "Random Forest Regressor\n", "Model performance for Training set\n", "- Root Mean Squared Error: 39021.6745\n", "- Mean Absolute Error: 26191.8907\n", "- R2 Score: 0.9889\n", "----------------------------------\n", "Model performance for Test set\n", "- Root Mean Squared Error: 99414.9808\n", "- Mean Absolute Error: 65855.1943\n", "- R2 Score: 0.9323\n", "===================================\n", "\n", "\n", "XGBRegressor\n", "Model performance for Training set\n", "- Root Mean Squared Error: 64604.3955\n", "- Mean Absolute Error: 46946.8468\n", "- R2 Score: 0.9694\n", "----------------------------------\n", "Model performance for Test set\n", "- Root Mean Squared Error: 93323.0762\n", "- Mean Absolute Error: 63986.2462\n", "- R2 Score: 0.9403\n", "===================================\n", "\n", "\n", "CatBoosting Regressor\n", "Model performance for Training set\n", "- Root Mean Squared Error: 74608.5033\n", "- Mean Absolute Error: 54468.1250\n", "- R2 Score: 0.9593\n", "----------------------------------\n", "Model performance for Test set\n", "- Root Mean Squared Error: 90935.6072\n", "- Mean Absolute Error: 63175.7719\n", "- R2 Score: 0.9433\n", "===================================\n", "\n", "\n", "AdaBoost Regressor\n", "Model performance for Training set\n", "- Root Mean Squared Error: 165023.1840\n", "- Mean Absolute Error: 136274.3513\n", "- R2 Score: 0.8007\n", "----------------------------------\n", "Model performance for Test set\n", "- Root Mean Squared Error: 172714.4153\n", "- Mean Absolute Error: 141780.4089\n", "- R2 Score: 0.7956\n", "===================================\n", "\n", "\n"]}], "source": ["models = {\n", "    \"Linear Regression\": LinearRegression(),\n", "    \"Lasso\": <PERSON><PERSON>(),\n", "    \"Ridge\": Ridge(),\n", "    \"K-Neighbors Regressor\": KNeighborsRegressor(),\n", "    \"Decision Tree\": DecisionTreeRegressor(),\n", "    \"Random Forest Regressor\": RandomForestRegressor(),\n", "    \"XGBRegressor\": XGBRegressor(), \n", "    \"CatBoosting Regressor\": <PERSON><PERSON>oostRegressor(verbose=False),\n", "    \"AdaBoost Regressor\": AdaBoostRegressor()\n", "}\n", "model_list = []\n", "r2_list =[]\n", "\n", "for i in range(len(list(models))):\n", "    model = list(models.values())[i]\n", "    model.fit(X_train, y_train) # Train model\n", "\n", "    # Make predictions\n", "    y_train_pred = model.predict(X_train)\n", "    y_test_pred = model.predict(X_test)\n", "    \n", "    # Evaluate Train and Test dataset\n", "    model_train_mae , model_train_rmse, model_train_r2 = evaluate_model(y_train, y_train_pred)\n", "\n", "    model_test_mae , model_test_rmse, model_test_r2 = evaluate_model(y_test, y_test_pred)\n", "\n", "    \n", "    print(list(models.keys())[i])\n", "    model_list.append(list(models.keys())[i])\n", "    \n", "    print('Model performance for Training set')\n", "    print(\"- Root Mean Squared Error: {:.4f}\".format(model_train_rmse))\n", "    print(\"- Mean Absolute Error: {:.4f}\".format(model_train_mae))\n", "    print(\"- R2 Score: {:.4f}\".format(model_train_r2))\n", "\n", "    print('----------------------------------')\n", "    \n", "    print('Model performance for Test set')\n", "    print(\"- Root Mean Squared Error: {:.4f}\".format(model_test_rmse))\n", "    print(\"- Mean Absolute Error: {:.4f}\".format(model_test_mae))\n", "    print(\"- R2 Score: {:.4f}\".format(model_test_r2))\n", "    r2_list.append(model_test_r2)\n", "    \n", "    print('='*35)\n", "    print('\\n')"]}, {"cell_type": "markdown", "id": "8ac46818", "metadata": {}, "source": ["**Results of All Models**"]}, {"cell_type": "code", "execution_count": 25, "id": "5f48929c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Model Name</th>\n", "      <th>R2_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>CatBoosting Regressor</td>\n", "      <td>0.943329</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>XGBRegressor</td>\n", "      <td>0.940314</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Random Forest Regressor</td>\n", "      <td>0.932268</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Regressor</td>\n", "      <td>0.927197</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Decision Tree</td>\n", "      <td>0.894295</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Linear Regression</td>\n", "      <td>0.834334</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>0.834332</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Ridge</td>\n", "      <td>0.834318</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>AdaBoost Regressor</td>\n", "      <td>0.795568</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                Model Name  R2_Score\n", "7    CatBoosting Regressor  0.943329\n", "6             XGBRegressor  0.940314\n", "5  Random Forest Regressor  0.932268\n", "3    K-Neighbors Regressor  0.927197\n", "4            Decision Tree  0.894295\n", "0        Linear Regression  0.834334\n", "1                    Lasso  0.834332\n", "2                    Ridge  0.834318\n", "8       AdaBoost Regressor  0.795568"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(list(zip(model_list, r2_list)), columns=['Model Name', 'R2_Score']).sort_values(by=[\"R2_Score\"],ascending=False)"]}, {"cell_type": "markdown", "id": "f3534db9", "metadata": {}, "source": ["**Here we can use Random Forest , XGBoost Regressor, CatBoost Regressor and K-Neighbours Regressor for Hyper Parameter Tuning**"]}, {"cell_type": "code", "execution_count": 26, "id": "893c4b00", "metadata": {}, "outputs": [], "source": ["#Initialize few parameter for Hyperparamter tuning\n", "knn_params = {\"n_neighbors\": [2, 3, 10, 20, 40, 50]}\n", "\n", "rf_params = {\"max_depth\": [5, 8, 15, None, 10],\n", "             \"max_features\": [5, 7, \"auto\", 8],\n", "             \"min_samples_split\": [2, 8, 15, 20],\n", "             \"n_estimators\": [100, 200, 500, 1000]}\n", "\n", "xgboost_params = {\"learning_rate\": [0.1, 0.01],\n", "                  \"max_depth\": [5, 8, 12, 20, 30],\n", "                  \"n_estimators\": [100, 200, 300],\n", "                  \"colsample_bytree\": [0.5, 0.8, 1, 0.3, 0.4]}\n", "\n", "cat_params = {\"learning_rate\": [0.1, 0.01, 0.06, 0.05],\n", "              \"max_depth\": [6, 8, 12, 20, 30]}"]}, {"cell_type": "code", "execution_count": 27, "id": "a46028bd", "metadata": {}, "outputs": [], "source": ["# Models list for Hyperparameter tuning\n", "randomcv_models = [('KNN', KNeighborsRegressor(), knn_params),\n", "                   (\"RF\", RandomForestRegressor(), rf_params),\n", "                   ('XGB<PERSON>t', XGBRegressor(), xgboost_params),\n", "                   ('<PERSON><PERSON><PERSON><PERSON>', Cat<PERSON>oostRegressor(verbose=False), cat_params)\n", "                   ]"]}, {"cell_type": "code", "execution_count": 28, "id": "0dc8830b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fitting 3 folds for each of 6 candidates, totalling 18 fits\n", "Fitting 3 folds for each of 100 candidates, totalling 300 fits\n", "Fitting 3 folds for each of 100 candidates, totalling 300 fits\n", "Fitting 3 folds for each of 20 candidates, totalling 60 fits\n", "---------------- Best Params for KNN -------------------\n", "{'n_neighbors': 10}\n", "---------------- Best Params for RF -------------------\n", "{'n_estimators': 1000, 'min_samples_split': 8, 'max_features': 8, 'max_depth': 15}\n", "---------------- Best Params for XGBoost -------------------\n", "{'n_estimators': 300, 'max_depth': 5, 'learning_rate': 0.1, 'colsample_bytree': 0.5}\n", "---------------- Best Params for CatBoost -------------------\n", "{'max_depth': 8, 'learning_rate': 0.05}\n"]}], "source": ["from sklearn.model_selection import RandomizedSearchCV\n", "\n", "model_param = {}\n", "for name, model, params in randomcv_models:\n", "    random = RandomizedSearchCV(estimator=model,\n", "                                   param_distributions=params,\n", "                                   n_iter=100,\n", "                                   cv=3,\n", "                                   verbose=2,\n", "                                   n_jobs=-1)\n", "    random.fit(X_train, y_train)\n", "    model_param[name] = random.best_params_\n", "\n", "for model_name in model_param:\n", "    print(f\"---------------- Best Params for {model_name} -------------------\")\n", "    print(model_param[model_name])"]}, {"cell_type": "markdown", "id": "9758d026", "metadata": {}, "source": ["### Retraining the Model with best Parameters"]}, {"cell_type": "code", "execution_count": 29, "id": "3103bce3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Random Forest Regressor\n", "Model performance for Training set\n", "- Root Mean Squared Error: 66549.7836\n", "- Mean Absolute Error: 47225.9463\n", "- R2 Score: 0.9676\n", "----------------------------------\n", "Model performance for Test set\n", "- Root Mean Squared Error: 93408.3146\n", "- Mean Absolute Error: 63784.8802\n", "- R2 Score: 0.9402\n", "===================================\n", "\n", "\n", "K-N<PERSON><PERSON><PERSON><PERSON>\n", "Model performance for Training set\n", "- Root Mean Squared Error: 90420.2335\n", "- Mean Absolute Error: 61129.7696\n", "- R2 Score: 0.9402\n", "----------------------------------\n", "Model performance for Test set\n", "- Root Mean Squared Error: 103790.7683\n", "- Mean Absolute Error: 69732.2494\n", "- R2 Score: 0.9262\n", "===================================\n", "\n", "\n", "XGBRegressor\n", "Model performance for Training set\n", "- Root Mean Squared Error: 74949.7150\n", "- Mean Absolute Error: 54543.4131\n", "- R2 Score: 0.9589\n", "----------------------------------\n", "Model performance for Test set\n", "- Root Mean Squared Error: 92106.7656\n", "- Mean Absolute Error: 63874.9049\n", "- R2 Score: 0.9419\n", "===================================\n", "\n", "\n", "CatBoosting Regressor\n", "Model performance for Training set\n", "- Root Mean Squared Error: 68704.9792\n", "- Mean Absolute Error: 50363.0108\n", "- R2 Score: 0.9654\n", "----------------------------------\n", "Model performance for Test set\n", "- Root Mean Squared Error: 90603.1036\n", "- Mean Absolute Error: 62679.3469\n", "- R2 Score: 0.9437\n", "===================================\n", "\n", "\n"]}], "source": ["models = {\n", "    \"Random Forest Regressor\": RandomForestRegressor(**model_param['RF'], n_jobs=-1),\n", "     \"K-Neighbors Regressor\": KNeighborsRegressor(**model_param['KNN'], n_jobs=-1),\n", "    \"XGBRegressor\": XGBRegressor(**model_param['XGBoost'], n_jobs=-1), \n", "     \"CatBoosting Regressor\": CatBoostRegressor(**model_param['CatBoost'],verbose=False)\n", "}\n", "\n", "model_list = []\n", "r2_list =[]\n", "\n", "for i in range(len(list(models))):\n", "    model = list(models.values())[i]\n", "    model.fit(X_train, y_train) # Train model\n", "\n", "    # Make predictions\n", "    y_train_pred = model.predict(X_train)\n", "    y_test_pred = model.predict(X_test)\n", "\n", "    model_train_mae , model_train_rmse, model_train_r2 = evaluate_model(y_train, y_train_pred)\n", "\n", "    model_test_mae , model_test_rmse, model_test_r2 = evaluate_model(y_test, y_test_pred)\n", "    \n", "    print(list(models.keys())[i])\n", "    model_list.append(list(models.keys())[i])\n", "    \n", "    print('Model performance for Training set')\n", "    print(\"- Root Mean Squared Error: {:.4f}\".format(model_train_rmse))\n", "    print(\"- Mean Absolute Error: {:.4f}\".format(model_train_mae))\n", "    print(\"- R2 Score: {:.4f}\".format(model_train_r2))\n", "\n", "    print('----------------------------------')\n", "    \n", "    print('Model performance for Test set')\n", "    print(\"- Root Mean Squared Error: {:.4f}\".format(model_test_rmse))\n", "    print(\"- Mean Absolute Error: {:.4f}\".format(model_test_mae))\n", "    print(\"- R2 Score: {:.4f}\".format(model_test_r2))\n", "    r2_list.append(model_test_r2)\n", "    \n", "    print('='*35)\n", "    print('\\n')"]}, {"cell_type": "code", "execution_count": 30, "id": "6714017b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Model Name</th>\n", "      <th>R2_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CatBoosting Regressor</td>\n", "      <td>0.943743</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>XGBRegressor</td>\n", "      <td>0.941860</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Random Forest Regressor</td>\n", "      <td>0.940205</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Regressor</td>\n", "      <td>0.926174</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                Model Name  R2_Score\n", "3    CatBoosting Regressor  0.943743\n", "2             XGBRegressor  0.941860\n", "0  Random Forest Regressor  0.940205\n", "1    K-Neighbors Regressor  0.926174"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(list(zip(model_list, r2_list*100)), columns=['Model Name', 'R2_Score']).sort_values(by=[\"R2_Score\"],ascending=False)"]}, {"cell_type": "markdown", "id": "109cccb4", "metadata": {}, "source": ["### Best Model is CATBoost Regressor with 94.37% R2 Score"]}, {"cell_type": "code", "execution_count": null, "id": "bb101545", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}, "vscode": {"interpreter": {"hash": "fe5057b33890e8dd303e21b19623a3798ffaa8a05dcdf7dd3a35472e2b83b2ee"}}}, "nbformat": 4, "nbformat_minor": 5}