grid_search:
  class: GridSearchCV
  module: sklearn.model_selection
  params:
    cv: 2
    verbose: 2
model_selection:
  module_0:
    class: XGBRegressor
    module: xgboost
    params:
      learning_rate: 0.01
      max_depth: 5
      n_estimators: 100
      colsample_bytree: 0.5
    search_param_grid:
      learning_rate:
      - 0.1
      - 0.02
      - 0.08
      max_depth:
      - 5
      - 8
      - 12
      n_estimators:
      - 100
      - 200
      - 400
      colsample_bytree:
      - 0.5
      - 0.8
      - 1
  module_1:
    class: RandomForestRegressor
    module: sklearn.ensemble
    params:
      n_jobs: -1
      n_estimators: 100
      max_depth: 5
      min_samples_split: 2
      max_features: sqrt
    search_param_grid:
      n_estimators:
      - 100
      - 200
      - 600
      max_depth:
      - 5
      - 20
      - 30
      min_samples_split:
      - 2
      - 8
      - 20
      max_features: 
      - sqrt
      - log2
      - 1.0
