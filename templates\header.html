<!doctype html>
<html lang="en">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script src="https://kit.fontawesome.com/778c911bcf.js" crossorigin="anonymous"></script>
    <style>
   
        .col-md-2 {
            margin-bottom: 20px;
            margin-top: 20px;
        }
    </style>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous">
    {% block head %}{% endblock %}
</head>
<style>

    table{
        -webkit-box-shadow: 0px 7px 16px -2px rgba(0,0,0,0.75);
-moz-box-shadow: 0px 7px 16px -2px rgba(0,0,0,0.75);
box-shadow: 0px 7px 16px -2px rgba(0,0,0,0.75);
width:100%;
overflow:scroll;
table-layout: fixed;
    width: 100%; 
 
    overflow:scroll;
    min-height: 400px; /* table height here */
  border-collapse: collapse;
    }
    
    th,td {
        text-align:left;
    word-wrap: break-word;
}
</style>
<body style="background-color: #f1f1f1;">



    <!---
    
    navbar code
    
    
    
    -->

    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarTogglerDemo03"
                aria-controls="navbarTogglerDemo03" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <a class="navbar-brand" href="/">Used Car Price Estimation</a>
            <div class="collapse navbar-collapse" id="navbarTogglerDemo03">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link active" aria-current="page" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'log' in request.path %} active {% endif %}" href="/logs">View Logs</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'artifact' in request.path %} active {% endif %}" href="/artifact">View Artifacts</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'saved_models' in request.path %} active {% endif %}" href="/saved_models">View Trained Model</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'predict' in request.path %} active {% endif %}" href="/predict">Estimate Car Price Value</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'predict' in request.path %} active {% endif %}" href="/view_experiment_hist">Experiment History</a>
                    </li>
                </ul>
            </div>
        </div>
        </div>
    </nav>



    <div class="row" style="margin-left: 5%;margin-right:5%;">
       
        <div class="col-md-2 col-sm-12 col-lg-2">
            <ul class=" list-group ">
                <li class="list-group-item py-1  {% if 'log' in request.path %} active {% endif %}">
                    <a  style=" text-decoration: none;color:black" href="/logs">View Logs</a>
                </li>
                <li class="list-group-item py-1 {% if 'artifact' in request.path %} active {% endif %}">
                    <a style=" text-decoration: none;color:rgb(0, 0, 0)" href="/artifact">View Artifacts</a>
                </li>
                <li class="list-group-item py-1 {% if 'saved_models' in request.path %} active {% endif %}">
                    <a style=" text-decoration: none;color:black" href="/saved_models">View Trained Model</a>
                </li>
                <li class="list-group-item py-1 {% if 'predict' in request.path %} active {% endif %}">
                    <a style=" text-decoration: none;color:black" href="/predict">Estimate Car Price Value</a>
                </li>
                <li class="list-group-item py-1 {% if 'train' in request.path %} active {% endif %}">
                    <a style=" text-decoration: none;color:black" href="/train">Train Car Price Estimator</a>
                </li>
                


                <li class="list-group-item py-1 {% if 'update_model_config' in request.path %} active {% endif %}">
                    <a style=" text-decoration: none;color:black" href="/update_model_config">Update Model Config</a>
                </li>
            </ul>

        </div>

        <div class="col-md-8 col-sm-12 col-lg-9" style="margin-top:20px;"> 
            {% block content %}


            {% endblock %}

        </div>

    </div>

    <!-- footer goes here-->




    <footer class="text-center text-white " style="background-color: #d4d2d2;">
        <!-- Grid container -->
        <div class="container pt-4">
            <!-- Section: Social media -->
            <section class="mb-4">
                <!-- Facebook -->
                <a class="btn btn-link btn-floating btn-lg text-dark m-1" href="#!" role="button"
                    data-mdb-ripple-color="dark"><i class="fab fa-facebook-f"></i></a>

                <!-- Twitter -->
                <a class="btn btn-link btn-floating btn-lg text-dark m-1" href="#!" role="button"
                    data-mdb-ripple-color="dark"><i class="fab fa-twitter"></i></a>

                <!-- Google -->
                <a class="btn btn-link btn-floating btn-lg text-dark m-1" href="#!" role="button"
                    data-mdb-ripple-color="dark"><i class="fab fa-google"></i></a>

                <!-- Instagram -->
                <a class="btn btn-link btn-floating btn-lg text-dark m-1" href="#!" role="button"
                    data-mdb-ripple-color="dark"><i class="fab fa-instagram"></i></a>

                <!-- Linkedin -->
                <a class="btn btn-link btn-floating btn-lg text-dark m-1" href="#!" role="button"
                    data-mdb-ripple-color="dark"><i class="fab fa-linkedin"></i></a>
                <!-- Github -->
                <a class="btn btn-link btn-floating btn-lg text-dark m-1" href="#!" role="button"
                    data-mdb-ripple-color="dark"><i class="fab fa-github"></i></a>
            </section>
            <!-- Section: Social media -->
        </div>
        <!-- Grid container -->

        <!-- Copyright -->
        <div class="text-center text-light p-3" style="background-color: rgba(3, 36, 67, 0.504);">
            © 2022 Copyright:
            <a class="text-light" href="https://ineuron.ai/">iNeuron Intelligence Pvt Limited</a>
        </div>
        <!-- Copyright -->
    </footer>


    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p"
        crossorigin="anonymous"></script>

</body>

</html>